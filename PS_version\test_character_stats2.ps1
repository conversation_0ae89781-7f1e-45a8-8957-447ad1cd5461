# Test script to check what character data is available from the Blizzard API

# Function to load configuration from JSON file
function Get-Config {
    try {
        $configPath = "..\config.json"
        if (Test-Path $configPath) {
            $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
            return $configContent
        } else {
            Write-Warning "Config file not found at $configPath, using default values"
            return $null
        }
    } catch {
        Write-Warning "Error loading config file: $_"
        return $null
    }
}

# Load configuration
$config = Get-Config

function Get-BattleNetAccessToken {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ClientId,
        
        [Parameter(Mandatory=$true)]
        [string]$ClientSecret,
        
        [Parameter(Mandatory=$true)]
        [string]$Region
    )

    $authUrl = "https://$Region.battle.net/oauth/token"
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("$ClientId`:$ClientSecret")))
    $headers = @{Authorization = "Basic $base64AuthInfo"}
    $body = @{grant_type = 'client_credentials'}

    try {
        $response = Invoke-RestMethod -Uri $authUrl -Method Post -Headers $headers -Body $body -ContentType 'application/x-www-form-urlencoded'
        Write-Host "✓ Token obtained successfully" -ForegroundColor Green
        return $response.access_token
    }
    catch {
        Write-Host "✗ Error getting access token: $_" -ForegroundColor Red
        return $null
    }
}

function Get-WoWCharacter {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}

function Get-WoWCharacterStatistics {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/statistics"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}

# Load API credentials
try {
    $api_cred = Import-Csv -Delimiter "," -Path .\csv_api.csv
    $token = Get-BattleNetAccessToken -ClientId $api_cred.client -ClientSecret $api_cred.secret -Region "eu"
    
    if ($token) {
        # Test with a specific character from your raiders list
        $testRealm = "stormscale"
        $testCharacter = "drexyl"
        
        Write-Host "Testing character: $testCharacter on $testRealm" -ForegroundColor Cyan
        
        # Get character profile
        $character = Get-WoWCharacter -AccessToken $token -Realm $testRealm -CharacterName $testCharacter
        
        if ($character) {
            Write-Host "`nCharacter Profile Properties:" -ForegroundColor Yellow
            $character | Get-Member -MemberType Properties | ForEach-Object {
                Write-Host "  $($_.Name)" -ForegroundColor White
            }
            
            # Check if stats are available in the character profile
            if ($character.PSObject.Properties.Name -contains "stats") {
                Write-Host "`nCharacter Stats from Profile:" -ForegroundColor Green
                $character.stats | Format-List
            } else {
                Write-Host "`nNo stats property found in character profile" -ForegroundColor Red
            }
        }
        
        # Get character statistics
        $characterStats = Get-WoWCharacterStatistics -AccessToken $token -Realm $testRealm -CharacterName $testCharacter
        
        if ($characterStats) {
            Write-Host "`nCharacter Statistics Properties:" -ForegroundColor Yellow
            $characterStats | Get-Member -MemberType Properties | ForEach-Object {
                Write-Host "  $($_.Name)" -ForegroundColor White
            }
            
            # Check if stats are available in the statistics endpoint
            if ($characterStats.PSObject.Properties.Name -contains "stats") {
                Write-Host "`nCharacter Stats from Statistics Endpoint:" -ForegroundColor Green
                $characterStats.stats | Format-List
            } else {
                Write-Host "`nNo stats property found in statistics endpoint" -ForegroundColor Red
            }
        }
    }
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
}
