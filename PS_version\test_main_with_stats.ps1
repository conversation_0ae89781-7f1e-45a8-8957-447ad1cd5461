# Test the main script with stats calculation for one character

cd .\PS_version\

# Function to load configuration from JSON file
function Get-Config {
    try {
        $configPath = "..\config.json"
        if (Test-Path $configPath) {
            $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
            return $configContent
        } else {
            Write-Warning "Config file not found at $configPath, using default values"
            return $null
        }
    } catch {
        Write-Warning "Error loading config file: $_"
        return $null
    }
}

# Load configuration
$config = Get-Config

function Get-BattleNetAccessToken {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ClientId,
        
        [Parameter(Mandatory=$true)]
        [string]$ClientSecret,
        
        [Parameter(Mandatory=$true)]
        [string]$Region
    )

    $authUrl        = "https://$Region.battle.net/oauth/token"
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("$ClientId`:$ClientSecret")))
    $headers        = @{Authorization = "Basic $base64AuthInfo"}
    $body           = @{grant_type = 'client_credentials'}

    try {
        $response   = Invoke-RestMethod -Uri $authUrl -Method Post -Headers $headers -Body $body -ContentType 'application/x-www-form-urlencoded'
        Write-Host "✓ Token obtained successfully" -ForegroundColor Green
        return $response.access_token
    }
    catch {
        Write-Host "✗ Error getting access token: $_" -ForegroundColor Red
        return $null
    }
}

function Get-WoWCharacter {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm          = $Realm.ToLower()
    $CharacterName  = $CharacterName.ToLower()
    
    $profileUrl     = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}

function Get-WoWCharacterEquipement {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AccessToken,
        
        [Parameter(Mandatory=$true)]
        [string]$Realm,
        
        [Parameter(Mandatory=$true)]
        [string]$CharacterName,
        
        [Parameter(Mandatory=$false)]
        [string]$Region = 'eu'
    )

    $Realm = $Realm.ToLower()
    $CharacterName = $CharacterName.ToLower()
    
    $profileUrl = "https://$Region.api.blizzard.com/profile/wow/character/$Realm/$CharacterName/equipment"
    
    $headers = @{
        'Authorization' = "Bearer $AccessToken"
        'Battlenet-Namespace' = "profile-$Region"
    }
    
    $queryParams = @{
        'namespace' = "profile-$Region"
        'locale' = 'en_GB'
    }

    try {
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $response = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        return $response
    }
    catch {
        Write-Host "✗ Request Error: $_" -ForegroundColor Red
        return $null
    }
}

try {
    $api_cred = Import-Csv -Delimiter "," -Path .\csv_api.csv
    $token = Get-BattleNetAccessToken -ClientId $api_cred.client -ClientSecret $api_cred.secret -Region "eu"
    
    if ($token) {
        # Test with one character
        $testRealm = "stormscale"
        $testCharacter = "drexyl"
        
        Write-Host "Testing stats calculation for $testCharacter on $testRealm" -ForegroundColor Cyan
        
        $character = Get-WoWCharacter -AccessToken $token -Realm $testRealm -CharacterName $testCharacter
        $characterEq = Get-WoWCharacterEquipement -AccessToken $token -Realm $testRealm -CharacterName $testCharacter
        
        if ($character -and $characterEq) {
            # Extract character stats from equipment
            $haste = 0
            $mastery = 0
            $versatility = 0
            $criticalStrike = 0
            
            # Calculate stats from all equipped items
            foreach ($item in $characterEq.equipped_items) {
                if ($item.stats) {
                    foreach ($stat in $item.stats) {
                        switch ($stat.type.type) {
                            "HASTE_RATING" { $haste += $stat.value }
                            "MASTERY_RATING" { $mastery += $stat.value }
                            "VERSATILITY_RATING" { $versatility += $stat.value }
                            "CRIT_RATING" { $criticalStrike += $stat.value }
                        }
                    }
                }
                
                # Also check gems in sockets for additional stats
                if ($item.sockets) {
                    foreach ($socket in $item.sockets) {
                        if ($socket.display_string) {
                            $gemText = $socket.display_string
                            if ($gemText -match '\+(\d+)\s+Haste') { $haste += [int]$matches[1] }
                            if ($gemText -match '\+(\d+)\s+Mastery') { $mastery += [int]$matches[1] }
                            if ($gemText -match '\+(\d+)\s+Versatility') { $versatility += [int]$matches[1] }
                            if ($gemText -match '\+(\d+)\s+Critical Strike') { $criticalStrike += [int]$matches[1] }
                        }
                    }
                }
            }
            
            Write-Host "Character: $($character.name)" -ForegroundColor Green
            Write-Host "Class: $($character.character_class.name)" -ForegroundColor White
            Write-Host "Spec: $($character.active_spec.name)" -ForegroundColor White
            Write-Host "Item Level: $($character.equipped_item_level)" -ForegroundColor White
            Write-Host "Stats:" -ForegroundColor Yellow
            Write-Host "  Haste: $haste" -ForegroundColor Cyan
            Write-Host "  Mastery: $mastery" -ForegroundColor Cyan
            Write-Host "  Versatility: $versatility" -ForegroundColor Cyan
            Write-Host "  Critical Strike: $criticalStrike" -ForegroundColor Cyan
            
            # Create a test output object like the main script would
            $testOutput = [pscustomobject]@{
                Charname = $character.name
                Realm = $character.realm.name
                Class = $character.character_class.name
                Spec = $character.active_spec.name
                ilvl = $character.equipped_item_level
                Haste = $haste
                Mastery = $mastery
                Versatility = $versatility
                Critical_Strike = $criticalStrike
            }
            
            Write-Host "`nTest output object:" -ForegroundColor Yellow
            $testOutput | Format-List
        }
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
