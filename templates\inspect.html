{% extends "base.html" %}

{% block title %}Character Inspect - Uproar{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-start align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-search text-accent me-2"></i>
                    Character Inspect
                </h1>
            </div>
        </div>
    </div>
</div>

<!-- Character Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Select Character to Inspect
                </h6>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-warning mb-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ error }}
                </div>
                {% endif %}

                <form method="GET" action="/inspect">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="character" class="form-label">Character</label>
                            <select class="form-select" id="character" name="character" required>
                                <option value="">Choose a character...</option>
                                {% for char_name in characters %}
                                    <option value="{{ char_name }}"
                                            {% if character and character.name == char_name %}selected{% endif %}>
                                        {{ char_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Inspect Character
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% if character %}
<!-- Character Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    {{ character.name }} - {{ character.realm }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Character Image -->
                    <div class="col-md-3">
                        {% if character.has_image %}
                            <img src="{{ url_for('character_images', filename=character.image_path.split('/')[-1]) }}"
                                 class="img-fluid rounded" alt="{{ character.name }}" style="max-height: 700px;">
                        {% else %}
                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                 style="height: 700px;">
                                <i class="fas fa-user fa-5x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Character Details -->
                    <div class="col-md-9">
                        <div class="row g-3">
                            <!-- Character Info Cards -->
                            <div class="col-md-6">
                                <div class="character-info-grid">
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-user me-1"></i>Race
                                        </div>
                                        <div class="info-value">{{ character.race }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-fist-raised me-1"></i>Class
                                        </div>
                                        <div class="info-value">{{ character.class_name }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-cog me-1"></i>Spec
                                        </div>
                                        <div class="info-value">{{ character.spec }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-users-cog me-1"></i>Role
                                        </div>
                                        <div class="info-value">
                                            <span class="badge role-{{ character.role.lower().replace(' ', '-') }}">
                                                {{ character.role }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-flag me-1"></i>Faction
                                        </div>
                                        <div class="info-value">{{ character.faction }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="character-info-grid">
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-star me-1"></i>Item Level
                                        </div>
                                        <div class="info-value">
                                            <span class="text-warning fw-bold fs-5">{{ character.calculated_ilvl }}</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-tshirt me-1"></i>Armor Type
                                        </div>
                                        <div class="info-value">{{ character.armor_type }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-gem me-1"></i>Tier Token
                                        </div>
                                        <div class="info-value">{{ character.tier_token }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-layer-group me-1"></i>Tier Pieces
                                        </div>
                                        <div class="info-value">
                                            <span class="badge bg-info">{{ character.tier_pieces }}/5</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="fas fa-chart-line me-1"></i>M+ Rating
                                        </div>
                                        <div class="info-value">
                                            <span class="text-success fw-bold fs-5">{{ character.rating }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gear Information -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tshirt me-2"></i>
                    Equipment
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Left Column -->
                    <div class="col-md-6">
                        <!-- Head -->
                        <div class="gear-slot mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Head:</strong>
                                {% if character.gear.head.tier == 1 %}
                                    <span class="badge bg-warning">Tier</span>
                                {% endif %}
                            </div>
                            <div class="gear-item">{{ character.gear.head.name }}</div>
                            {% if character.gear.head.socket == 1 %}
                                <small class="text-info">{{ character.gear.head.gem }}</small>
                            {% endif %}
                            {% if character.gear.head.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Neck -->
                        <div class="gear-slot mb-3">
                            <strong>Neck:</strong>
                            <div class="gear-item">{{ character.gear.neck.name }}</div>
                            {% if character.gear.neck.sockets > 0 %}
                                <small class="text-info">
                                    {% if character.gear.neck.gem1 %}{{ character.gear.neck.gem1 }}{% endif %}
                                    {% if character.gear.neck.gem2 %}, {{ character.gear.neck.gem2 }}{% endif %}
                                </small>
                            {% endif %}
                        </div>

                        <!-- Shoulders -->
                        <div class="gear-slot mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Shoulders:</strong>
                                {% if character.gear.shoulders.tier == 1 %}
                                    <span class="badge bg-warning">Tier</span>
                                {% endif %}
                            </div>
                            <div class="gear-item">{{ character.gear.shoulders.name }}</div>
                            {% if character.gear.shoulders.socket == 1 %}
                                <small class="text-info">{{ character.gear.shoulders.gem }}</small>
                            {% endif %}
                        </div>

                        <!-- Chest -->
                        <div class="gear-slot mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Chest:</strong>
                                {% if character.gear.chest.tier == 1 %}
                                    <span class="badge bg-warning">Tier</span>
                                {% endif %}
                            </div>
                            <div class="gear-item">{{ character.gear.chest.name }}</div>
                            {% if character.gear.chest.socket == 1 %}
                                <small class="text-info">{{ character.gear.chest.gem }}</small>
                            {% endif %}
                            {% if character.gear.chest.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Waist -->
                        <div class="gear-slot mb-3">
                            <strong>Waist:</strong>
                            <div class="gear-item">{{ character.gear.waist.name }}</div>
                            {% if character.gear.waist.socket == 1 %}
                                <small class="text-info">{{ character.gear.waist.gem }}</small>
                            {% endif %}
                        </div>

                        <!-- Legs -->
                        <div class="gear-slot mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Legs:</strong>
                                {% if character.gear.legs.tier == 1 %}
                                    <span class="badge bg-warning">Tier</span>
                                {% endif %}
                            </div>
                            <div class="gear-item">{{ character.gear.legs.name }}</div>
                            {% if character.gear.legs.socket == 1 %}
                                <small class="text-info">{{ character.gear.legs.gem }}</small>
                            {% endif %}
                            {% if character.gear.legs.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Feet -->
                        <div class="gear-slot mb-3">
                            <strong>Feet:</strong>
                            <div class="gear-item">{{ character.gear.feet.name }}</div>
                            {% if character.gear.feet.socket == 1 %}
                                <small class="text-info">{{ character.gear.feet.gem }}</small>
                            {% endif %}
                            {% if character.gear.feet.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="col-md-6">
                        <!-- Wrists -->
                        <div class="gear-slot mb-3">
                            <strong>Wrists:</strong>
                            <div class="gear-item">{{ character.gear.wrists.name }}</div>
                            {% if character.gear.wrists.socket == 1 %}
                                <small class="text-info">{{ character.gear.wrists.gem }}</small>
                            {% endif %}
                            {% if character.gear.wrists.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Hands -->
                        <div class="gear-slot mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Gloves:</strong>
                                {% if character.gear.hands.tier == 1 %}
                                    <span class="badge bg-warning">Tier</span>
                                {% endif %}
                            </div>
                            <div class="gear-item">{{ character.gear.hands.name }}</div>
                            {% if character.gear.hands.socket == 1 %}
                                <small class="text-info">{{ character.gear.hands.gem }}</small>
                            {% endif %}
                            {% if character.gear.hands.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Rings -->
                        <div class="gear-slot mb-3">
                            <strong>Ring 1:</strong>
                            <div class="gear-item">{{ character.gear.finger1.name }}</div>
                            {% if character.gear.finger1.sockets > 0 %}
                                <small class="text-info">
                                    {% if character.gear.finger1.gem1 %}{{ character.gear.finger1.gem1 }}{% endif %}
                                    {% if character.gear.finger1.gem2 %}, {{ character.gear.finger1.gem2 }}{% endif %}
                                </small>
                            {% endif %}
                            {% if character.gear.finger1.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <div class="gear-slot mb-3">
                            <strong>Ring 2:</strong>
                            <div class="gear-item">{{ character.gear.finger2.name }}</div>
                            {% if character.gear.finger2.sockets > 0 %}
                                <small class="text-info">
                                    {% if character.gear.finger2.gem1 %}{{ character.gear.finger2.gem1 }}{% endif %}
                                    {% if character.gear.finger2.gem2 %}, {{ character.gear.finger2.gem2 }}{% endif %}
                                </small>
                            {% endif %}
                            {% if character.gear.finger2.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Trinkets -->
                        <div class="gear-slot mb-3">
                            <strong>Trinket 1:</strong>
                            <div class="gear-item">{{ character.gear.trinket1.name }}</div>
                        </div>

                        <div class="gear-slot mb-3">
                            <strong>Trinket 2:</strong>
                            <div class="gear-item">{{ character.gear.trinket2.name }}</div>
                        </div>

                        <!-- Back -->
                        <div class="gear-slot mb-3">
                            <strong>Back:</strong>
                            <div class="gear-item">{{ character.gear.back.name }}</div>
                            {% if character.gear.back.socket == 1 %}
                                <small class="text-info">{{ character.gear.back.gem }}</small>
                            {% endif %}
                            {% if character.gear.back.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        <!-- Weapons -->
                        <div class="gear-slot mb-3">
                            <strong>Main Hand:</strong>
                            <div class="gear-item">{{ character.gear.main_hand.name }}</div>
                            {% if character.gear.main_hand.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>

                        {% if character.gear.off_hand.name %}
                        <div class="gear-slot mb-3">
                            <strong>Off Hand:</strong>
                            <div class="gear-item">{{ character.gear.off_hand.name }}</div>
                            {% if character.gear.off_hand.enchant == 1 %}
                                <small class="text-success">Enchanted</small>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_css %}
<style>
.gear-slot {
    border-left: 3px solid #007bff;
    padding-left: 10px;
}

.gear-item {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 2px;
}

.role-tank { background-color: #0d6efd; }
.role-healer { background-color: #198754; }
.role-melee-dps { background-color: #dc3545; }
.role-ranged-dps { background-color: #fd7e14; }

/* Character Info Grid Styles */
.character-info-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
}

.info-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    transition: all 0.2s ease;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 60px;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.info-label {
    font-size: 0.85rem;
    color: #adb5bd;
    margin-bottom: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    color: #f8f9fa;
    font-weight: 600;
}

/* Dark theme adjustments */
body.dark-theme .info-item {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.08);
}

body.dark-theme .info-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
}
</style>
{% endblock %}
