# Test script to verify stats calculation

# Load API credentials
try {
    $api_cred = Import-Csv -Delimiter "," -Path .\csv_api.csv
    
    # Get token
    $authUrl = "https://eu.battle.net/oauth/token"
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("$($api_cred.client):$($api_cred.secret)")))
    $headers = @{Authorization = "Basic $base64AuthInfo"}
    $body = @{grant_type = 'client_credentials'}
    
    $response = Invoke-RestMethod -Uri $authUrl -Method Post -Headers $headers -Body $body -ContentType 'application/x-www-form-urlencoded'
    $token = $response.access_token
    
    if ($token) {
        Write-Host "Token obtained successfully" -ForegroundColor Green
        
        # Test with a character from your list
        $testRealm = "stormscale"
        $testCharacter = "drexyl"
        
        # Get character equipment
        $profileUrl = "https://eu.api.blizzard.com/profile/wow/character/$testRealm/$testCharacter/equipment"
        
        $headers = @{
            'Authorization' = "Bearer $token"
            'Battlenet-Namespace' = "profile-eu"
        }
        
        $queryParams = @{
            'namespace' = "profile-eu"
            'locale' = 'en_GB'
        }
        
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $characterEq = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        
        if ($characterEq -and $characterEq.equipped_items) {
            Write-Host "Equipment data retrieved successfully" -ForegroundColor Green
            
            # Calculate stats using the same logic as in main script
            $haste = 0
            $mastery = 0
            $versatility = 0
            $criticalStrike = 0
            
            Write-Host "`nCalculating stats from equipped items:" -ForegroundColor Yellow
            
            foreach ($item in $characterEq.equipped_items) {
                $itemHaste = 0
                $itemMastery = 0
                $itemVers = 0
                $itemCrit = 0
                
                if ($item.stats) {
                    foreach ($stat in $item.stats) {
                        switch ($stat.type.type) {
                            "HASTE_RATING" { 
                                $haste += $stat.value 
                                $itemHaste += $stat.value
                            }
                            "MASTERY_RATING" { 
                                $mastery += $stat.value 
                                $itemMastery += $stat.value
                            }
                            "VERSATILITY_RATING" { 
                                $versatility += $stat.value 
                                $itemVers += $stat.value
                            }
                            "CRIT_RATING" { 
                                $criticalStrike += $stat.value 
                                $itemCrit += $stat.value
                            }
                        }
                    }
                }
                
                # Also check gems in sockets for additional stats
                if ($item.sockets) {
                    foreach ($socket in $item.sockets) {
                        if ($socket.display_string) {
                            $gemText = $socket.display_string
                            if ($gemText -match '\+(\d+)\s+Haste') { 
                                $haste += [int]$matches[1] 
                                $itemHaste += [int]$matches[1]
                            }
                            if ($gemText -match '\+(\d+)\s+Mastery') { 
                                $mastery += [int]$matches[1] 
                                $itemMastery += [int]$matches[1]
                            }
                            if ($gemText -match '\+(\d+)\s+Versatility') { 
                                $versatility += [int]$matches[1] 
                                $itemVers += [int]$matches[1]
                            }
                            if ($gemText -match '\+(\d+)\s+Critical Strike') { 
                                $criticalStrike += [int]$matches[1] 
                                $itemCrit += [int]$matches[1]
                            }
                        }
                    }
                }
                
                # Show stats per item if any
                if ($itemHaste -gt 0 -or $itemMastery -gt 0 -or $itemVers -gt 0 -or $itemCrit -gt 0) {
                    Write-Host "  $($item.name): H=$itemHaste M=$itemMastery V=$itemVers C=$itemCrit" -ForegroundColor White
                }
            }
            
            Write-Host "`nTotal calculated stats for ${testCharacter}:" -ForegroundColor Green
            Write-Host "  Haste: $haste" -ForegroundColor Cyan
            Write-Host "  Mastery: $mastery" -ForegroundColor Cyan
            Write-Host "  Versatility: $versatility" -ForegroundColor Cyan
            Write-Host "  Critical Strike: $criticalStrike" -ForegroundColor Cyan
        } else {
            Write-Host "No equipment data found" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
