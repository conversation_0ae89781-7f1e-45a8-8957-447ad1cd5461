# Test script to check what equipment data is available

# Load API credentials
try {
    $api_cred = Import-Csv -Delimiter "," -Path .\csv_api.csv
    
    # Get token (simplified version)
    $authUrl = "https://eu.battle.net/oauth/token"
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("$($api_cred.client):$($api_cred.secret)")))
    $headers = @{Authorization = "Basic $base64AuthInfo"}
    $body = @{grant_type = 'client_credentials'}
    
    $response = Invoke-RestMethod -Uri $authUrl -Method Post -Headers $headers -Body $body -ContentType 'application/x-www-form-urlencoded'
    $token = $response.access_token
    
    if ($token) {
        Write-Host "Token obtained successfully" -ForegroundColor Green
        
        # Test with a character from your list
        $testRealm = "stormscale"
        $testCharacter = "drexyl"
        
        # Get character equipment
        $profileUrl = "https://eu.api.blizzard.com/profile/wow/character/$testRealm/$testCharacter/equipment"
        
        $headers = @{
            'Authorization' = "Bearer $token"
            'Battlenet-Namespace' = "profile-eu"
        }
        
        $queryParams = @{
            'namespace' = "profile-eu"
            'locale' = 'en_GB'
        }
        
        $queryString = [System.Web.HttpUtility]::ParseQueryString('')
        foreach ($param in $queryParams.GetEnumerator()) {
            $queryString[$param.Key] = $param.Value
        }
        
        $uriBuilder = [System.UriBuilder]$profileUrl
        $uriBuilder.Query = $queryString.ToString()
        
        $equipment = Invoke-RestMethod -Uri $uriBuilder.Uri.ToString() -Headers $headers -Method Get
        
        if ($equipment -and $equipment.equipped_items) {
            Write-Host "Equipment data retrieved successfully" -ForegroundColor Green
            Write-Host "Number of equipped items: $($equipment.equipped_items.Count)" -ForegroundColor Yellow
            
            # Check the first item to see what properties are available
            $firstItem = $equipment.equipped_items[0]
            Write-Host "`nFirst item properties:" -ForegroundColor Yellow
            $firstItem | Get-Member -MemberType Properties | ForEach-Object {
                Write-Host "  $($_.Name)" -ForegroundColor White
            }
            
            # Check if any item has stats
            Write-Host "`nChecking for stats in items:" -ForegroundColor Yellow
            foreach ($item in $equipment.equipped_items) {
                if ($item.stats) {
                    Write-Host "Item $($item.name) has stats:" -ForegroundColor Green
                    $item.stats | ForEach-Object {
                        Write-Host "  $($_.type.name): $($_.value)" -ForegroundColor White
                    }
                } else {
                    Write-Host "Item $($item.name) has no stats property" -ForegroundColor Red
                }
            }
            
            # Output first item details for inspection
            Write-Host "`nFirst item details:" -ForegroundColor Yellow
            $firstItem | ConvertTo-Json -Depth 5
        } else {
            Write-Host "No equipment data found" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
